export interface FlinksConfig {
  apiUrl: string;
  customerId: string;
  bearerToken: string;
  authKey: string;
  apiKey: string;
}

export interface FlinksApiResponse<T = any> {
  responseType: string;
  httpStatusCode: number;
  data?: T;
  error?: string;
  message?: string;
  requestId?: string;
}

export interface FlinksAccountHolder {
  name?: string;
  email?: string;
  phoneNumber?: string;
  address?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  country?: string;
  dateOfBirth?: string;
}

export interface FlinksBalance {
  available: number;
  current: number;
  limit?: number;
}

export interface FlinksTransaction {
  id: string;
  description: string;
  amount: number;
  date: string;
  category?: string;
  code?: string;
}

export interface FlinksAccount {
  id: string;
  title: string;
  accountNumber: string;
  type: string;
  category: string;
  currency: string;
  transitNumber?: string;
  institutionNumber?: string;
  balance: FlinksBalance;
  holder?: FlinksAccountHolder;
  transactions?: FlinksTransaction[];
}

export interface FlinksLogin {
  id: string;
  username: string;
  type: string;
  lastRefresh: string;
  isScheduledRefresh: boolean;
}

export interface FlinksInstitution {
  id: string;
  name: string;
  logoUrl?: string;
  transitNumber?: string;
  address?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  country?: string;
  website?: string;
  phoneNumber?: string;
}

export interface GetAccountsDetailResponse {
  responseType: string;
  httpStatusCode: number;
  accounts: FlinksAccount[];
  login: FlinksLogin;
  institution: string;
  requestId: string;
}

export interface GetAccountsSummaryResponse {
  responseType: string;
  httpStatusCode: number;
  accounts: FlinksAccount[];
  login: FlinksLogin;
  institution: string;
  requestId: string;
}

export interface GetInstitutionsResponse {
  institutions: FlinksInstitution[];
}

export interface ProcessedAccountData {
  accountHolder: {
    customerId: string;
    userId: string;
    firstName: string;
    lastName: string;
    email?: string;
    phoneNumber?: string;
    addressLine1?: string;
    city?: string;
    province?: string;
    postalCode?: string;
    country?: string;
    dateOfBirth?: Date;
  };
  institutionAccount: {
    accountNumber: string;
    balance: number;
    currentBalance: number;
    availableBalance: number;
    currency: string;
    routingNumber: string;
    accountType?: string;
    institutionId: string;
    userId: string;
    accessToken: string;
    latencyKey?: string;
  };
  bankInstitution: {
    name: string;
    logoUrl?: string;
    flinksInstitutionId: string;
    transitNumber?: string;
    address?: string;
    city?: string;
    province?: string;
    postalCode?: string;
    country?: string;
    website?: string;
    phoneNumber?: string;
  };
}

export interface FlinksApiRequest {
  loginId: string;
  requestId?: string;
  withTransactions?: boolean;
  withAccountIdentity?: boolean;
  withBalance?: boolean;
}

export interface FlinksErrorResponse {
  error: string;
  message: string;
  statusCode: number;
  requestId?: string;
}
