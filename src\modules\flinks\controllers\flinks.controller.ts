import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { FlinksApiService } from '../services/flinks-api.service';
import { FlinksDataProcessorService } from '../services/flinks-data-processor.service';
import {
  GetAccountsDetailRequestDto,
  GetAccountsSummaryRequestDto,
  ProcessFlinksDataRequestDto,
  GetAccountsDetailResponseDto,
  GetAccountsSummaryResponseDto,
  GetInstitutionsResponseDto,
} from '../dto/flinks.dto';

@ApiTags('Flinks Integration')
@Controller('flinks')
export class FlinksController {
  private readonly logger = new Logger(FlinksController.name);

  constructor(
    private readonly flinksApiService: FlinksApiService,
    private readonly flinksDataProcessor: FlinksDataProcessorService,
  ) {}

  @Post('accounts/detail')
  @ApiOperation({ summary: 'Get detailed account information from Flinks' })
  @ApiResponse({ status: 200, description: 'Account details retrieved successfully', type: GetAccountsDetailResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid request parameters' })
  @ApiResponse({ status: 503, description: 'Flinks API unavailable' })
  async getAccountsDetail(@Body() request: GetAccountsDetailRequestDto): Promise<GetAccountsDetailResponseDto> {
    this.logger.log(`Getting account details for loginId: ${request.loginId}`);

    try {
      const response = await this.flinksApiService.getAccountsDetail(
        request.loginId,
        request.requestId,
        request.withTransactions,
        request.withAccountIdentity,
        request.withBalance,
      );

      return response;
    } catch (error) {
      this.logger.error(`Failed to get account details: ${error.message}`);
      throw error;
    }
  }

  @Post('accounts/summary')
  @ApiOperation({ summary: 'Get account summary from Flinks' })
  @ApiResponse({ status: 200, description: 'Account summary retrieved successfully', type: GetAccountsSummaryResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid request parameters' })
  @ApiResponse({ status: 503, description: 'Flinks API unavailable' })
  async getAccountsSummary(@Body() request: GetAccountsSummaryRequestDto): Promise<GetAccountsSummaryResponseDto> {
    this.logger.log(`Getting account summary for loginId: ${request.loginId}`);

    try {
      const response = await this.flinksApiService.getAccountsSummary(
        request.loginId,
        request.requestId,
      );

      return response;
    } catch (error) {
      this.logger.error(`Failed to get account summary: ${error.message}`);
      throw error;
    }
  }

  @Get('institutions')
  @ApiOperation({ summary: 'Get all supported institutions from Flinks' })
  @ApiResponse({ status: 200, description: 'Institutions retrieved successfully', type: GetInstitutionsResponseDto })
  @ApiResponse({ status: 503, description: 'Flinks API unavailable' })
  async getInstitutions(): Promise<GetInstitutionsResponseDto> {
    this.logger.log('Getting all institutions from Flinks');

    try {
      const response = await this.flinksApiService.getInstitutions();
      return response;
    } catch (error) {
      this.logger.error(`Failed to get institutions: ${error.message}`);
      throw error;
    }
  }

  @Get('institutions/routing/:routingNumber')
  @ApiOperation({ summary: 'Get institution by routing number' })
  @ApiParam({ name: 'routingNumber', description: 'Bank routing number' })
  @ApiResponse({ status: 200, description: 'Institution found' })
  @ApiResponse({ status: 404, description: 'Institution not found' })
  @ApiResponse({ status: 503, description: 'Flinks API unavailable' })
  async getInstitutionByRoutingNumber(@Param('routingNumber') routingNumber: string) {
    this.logger.log(`Getting institution for routing number: ${routingNumber}`);

    try {
      const response = await this.flinksApiService.getInstitutionByRoutingNumber(routingNumber);
      return response;
    } catch (error) {
      this.logger.error(`Failed to get institution by routing number: ${error.message}`);
      throw error;
    }
  }

  @Post('process-data')
  @ApiOperation({ summary: 'Process and save Flinks account data to database' })
  @ApiResponse({ status: 200, description: 'Data processed and saved successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request parameters' })
  @ApiResponse({ status: 503, description: 'Flinks API unavailable' })
  async processFlinksData(@Body() request: ProcessFlinksDataRequestDto) {
    this.logger.log(`Processing Flinks data for user ${request.userId}, customer ${request.customerId}`);

    try {
      // First, get the detailed account information from Flinks
      const flinksResponse = await this.flinksApiService.getAccountsDetail(
        request.loginId,
        request.requestId,
        true, // withTransactions
        true, // withAccountIdentity
        true, // withBalance
      );

      // Process and save the data to our database
      const processedData = await this.flinksDataProcessor.processAndSaveAccountData(
        request.userId,
        request.customerId,
        flinksResponse,
      );

      return {
        success: true,
        message: 'Flinks data processed and saved successfully',
        data: {
          accountHoldersCount: processedData.accountHolders.length,
          institutionAccountsCount: processedData.institutionAccounts.length,
          bankInstitution: processedData.bankInstitution.name,
          requestId: flinksResponse.requestId,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to process Flinks data: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to process Flinks data',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('validate-login/:loginId')
  @ApiOperation({ summary: 'Validate a Flinks login ID' })
  @ApiParam({ name: 'loginId', description: 'Flinks login ID to validate' })
  @ApiResponse({ status: 200, description: 'Login ID validation result' })
  async validateLoginId(@Param('loginId') loginId: string) {
    this.logger.log(`Validating login ID: ${loginId}`);

    try {
      const isValid = await this.flinksApiService.validateLoginId(loginId);
      return {
        loginId,
        isValid,
        message: isValid ? 'Login ID is valid' : 'Login ID is invalid or expired',
      };
    } catch (error) {
      this.logger.error(`Failed to validate login ID: ${error.message}`);
      return {
        loginId,
        isValid: false,
        message: 'Failed to validate login ID',
        error: error.message,
      };
    }
  }

  @Get('user/:userId/account-holders')
  @ApiOperation({ summary: 'Get account holders for a user' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'Account holders retrieved successfully' })
  async getUserAccountHolders(@Param('userId') userId: string) {
    this.logger.log(`Getting account holders for user: ${userId}`);

    try {
      const accountHolders = await this.flinksDataProcessor.getAccountHoldersByUser(userId);
      return {
        success: true,
        data: accountHolders,
        count: accountHolders.length,
      };
    } catch (error) {
      this.logger.error(`Failed to get account holders: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to get account holders',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('user/:userId/institution-accounts')
  @ApiOperation({ summary: 'Get institution accounts for a user' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'Institution accounts retrieved successfully' })
  async getUserInstitutionAccounts(@Param('userId') userId: string) {
    this.logger.log(`Getting institution accounts for user: ${userId}`);

    try {
      const accounts = await this.flinksDataProcessor.getInstitutionAccountsByUser(userId);
      return {
        success: true,
        data: accounts,
        count: accounts.length,
      };
    } catch (error) {
      this.logger.error(`Failed to get institution accounts: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to get institution accounts',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('bank-institutions')
  @ApiOperation({ summary: 'Get all bank institutions from database' })
  @ApiResponse({ status: 200, description: 'Bank institutions retrieved successfully' })
  async getBankInstitutions() {
    this.logger.log('Getting all bank institutions from database');

    try {
      const institutions = await this.flinksDataProcessor.getBankInstitutions();
      return {
        success: true,
        data: institutions,
        count: institutions.length,
      };
    } catch (error) {
      this.logger.error(`Failed to get bank institutions: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to get bank institutions',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
