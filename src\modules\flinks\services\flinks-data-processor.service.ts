import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import {
  GetAccountsDetailResponse,
  FlinksAccount,
  FlinksAccountHolder,
  ProcessedAccountData,
} from '../interfaces/flinks.interface';

@Injectable()
export class FlinksDataProcessorService {
  private readonly logger = new Logger(FlinksDataProcessorService.name);

  constructor(private readonly prisma: PrismaService) {}

  async processAndSaveAccountData(
    userId: string,
    customerId: string,
    flinksResponse: GetAccountsDetailResponse,
  ): Promise<{
    accountHolders: any[];
    institutionAccounts: any[];
    bankInstitution: any;
  }> {
    this.logger.log(`Processing Flinks data for user ${userId}, customer ${customerId}`);

    try {
      // First, ensure the bank institution exists
      const bankInstitution = await this.ensureBankInstitution(flinksResponse.institution);

      const processedData: {
        accountHolders: any[];
        institutionAccounts: any[];
      } = {
        accountHolders: [],
        institutionAccounts: [],
      };

      // Process each account
      for (const account of flinksResponse.accounts) {
        const { accountHolder, institutionAccount } = await this.processAccount(
          userId,
          customerId,
          account,
          bankInstitution.id,
          flinksResponse.login.id,
        );

        processedData.accountHolders.push(accountHolder);
        processedData.institutionAccounts.push(institutionAccount);
      }

      this.logger.log(
        `Successfully processed ${processedData.accountHolders.length} accounts for user ${userId}`,
      );

      return {
        ...processedData,
        bankInstitution,
      };
    } catch (error) {
      this.logger.error(`Failed to process Flinks data: ${error.message}`);
      throw error;
    }
  }

  private async ensureBankInstitution(institutionName: string): Promise<any> {
    // Try to find existing institution by name
    let institution = await this.prisma.bankInstitution.findFirst({
      where: { name: institutionName },
    });

    if (!institution) {
      // Create new institution if it doesn't exist
      institution = await this.prisma.bankInstitution.create({
        data: {
          name: institutionName,
          flinksInstitutionId: institutionName, // Use name as ID for now
        },
      });
      this.logger.log(`Created new bank institution: ${institutionName}`);
    }

    return institution;
  }

  private async processAccount(
    userId: string,
    customerId: string,
    account: FlinksAccount,
    institutionId: string,
    accessToken: string,
  ): Promise<{
    accountHolder: any;
    institutionAccount: any;
  }> {
    // Extract account holder information
    const holderData = this.extractAccountHolderData(account.holder, userId, customerId);

    // Create or update account holder
    const accountHolder = await this.prisma.accountHolder.upsert({
      where: {
        customerId_userId: {
          customerId,
          userId,
        },
      },
      update: {
        ...holderData,
        updatedAt: new Date(),
      },
      create: holderData,
    });

    // Extract institution account data
    const accountData = this.extractInstitutionAccountData(
      account,
      institutionId,
      userId,
      accessToken,
    );

    // Create or update institution account
    const institutionAccount = await this.prisma.institutionAccount.upsert({
      where: {
        accountNumber_institutionId: {
          accountNumber: account.accountNumber,
          institutionId,
        },
      },
      update: {
        ...accountData,
        updatedAt: new Date(),
      },
      create: accountData,
    });

    // Link account holder to institution account if not already linked
    await this.linkAccountHolderToInstitutionAccount(accountHolder.id, institutionAccount.id);

    return { accountHolder, institutionAccount };
  }

  private extractAccountHolderData(
    holder: FlinksAccountHolder | undefined,
    userId: string,
    customerId: string,
  ): any {
    const nameParts = holder?.name?.split(' ') || ['', ''];
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    return {
      customerId,
      userId,
      firstName,
      lastName,
      email: holder?.email || null,
      phoneNumber: holder?.phoneNumber || null,
      addressLine1: holder?.address || null,
      city: holder?.city || null,
      province: holder?.province || null,
      postalCode: holder?.postalCode || null,
      country: holder?.country || null,
      dateOfBirth: holder?.dateOfBirth ? new Date(holder.dateOfBirth) : null,
    };
  }

  private extractInstitutionAccountData(
    account: FlinksAccount,
    institutionId: string,
    userId: string,
    accessToken: string,
  ): any {
    const routingNumber = account.transitNumber && account.institutionNumber 
      ? `${account.transitNumber}${account.institutionNumber}`
      : account.transitNumber || account.institutionNumber || '';

    return {
      accountNumber: account.accountNumber,
      balance: account.balance.current,
      currentBalance: account.balance.current,
      availableBalance: account.balance.available,
      currency: account.currency,
      routingNumber,
      accountType: account.type,
      institutionId,
      userId,
      accessToken,
      isFlinks: true,
      latencyKey: account.id,
    };
  }

  private async linkAccountHolderToInstitutionAccount(
    accountHolderId: string,
    institutionAccountId: string,
  ): Promise<void> {
    // Check if the link already exists
    const existingLink = await this.prisma.institutionAccount.findFirst({
      where: {
        id: institutionAccountId,
        accountHolders: {
          some: {
            id: accountHolderId,
          },
        },
      },
    });

    if (!existingLink) {
      // Create the link
      await this.prisma.institutionAccount.update({
        where: { id: institutionAccountId },
        data: {
          accountHolders: {
            connect: { id: accountHolderId },
          },
        },
      });
    }
  }

  async getAccountHoldersByUser(userId: string): Promise<any[]> {
    return this.prisma.accountHolder.findMany({
      where: { userId },
      include: {
        customer: true,
        user: true,
        institutionAccounts: {
          include: {
            institution: true,
          },
        },
      },
    });
  }

  async getInstitutionAccountsByUser(userId: string): Promise<any[]> {
    return this.prisma.institutionAccount.findMany({
      where: { userId },
      include: {
        institution: true,
        accountHolders: true,
        user: true,
      },
    });
  }

  async getBankInstitutions(): Promise<any[]> {
    return this.prisma.bankInstitution.findMany({
      include: {
        institutionAccounts: true,
      },
    });
  }
}
