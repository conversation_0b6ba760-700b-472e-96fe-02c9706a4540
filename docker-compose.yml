version: '3.8'
services:
  api:
    build: .
    container_name: openbank-api
    ports:
      - '3001:3070'
    env_file:
      - .env
    depends_on:
      - db
    environment:
      - DATABASE_URL=**************************************/postgres

  db:
    image: postgres:15-alpine
    container_name: openbank-db
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    ports:
      - '5432:5432'
    volumes:
      - pgdata:/var/lib/postgresql/data

volumes:
  pgdata:
