import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '../../../config/config.service';
import {
  FlinksConfig,
  FlinksApiResponse,
  GetAccountsDetailResponse,
  GetAccountsSummaryResponse,
  GetInstitutionsResponse,
  FlinksInstitution,
  FlinksErrorResponse,
} from '../interfaces/flinks.interface';

@Injectable()
export class FlinksApiService {
  private readonly logger = new Logger(FlinksApiService.name);
  private readonly config: FlinksConfig;

  constructor(private readonly configService: ConfigService) {
    this.config = {
      apiUrl: this.configService.get('FLINKS_API_URL'),
      customerId: this.configService.get('FLINKS_CUSTOMER_ID'),
      bearerToken: this.configService.get('FLINKS_BEARER_TOKEN'),
      authKey: this.configService.get('FLINKS_AUTH_KEY'),
      apiKey: this.configService.get('FLINKS_API_KEY'),
    };

    this.validateConfig();
  }

  private validateConfig(): void {
    const requiredFields = ['apiUrl', 'customerId', 'bearerToken', 'authKey', 'apiKey'];
    const missingFields = requiredFields.filter(field => !this.config[field as keyof FlinksConfig]);

    if (missingFields.length > 0) {
      throw new Error(`Missing Flinks configuration: ${missingFields.join(', ')}`);
    }
  }

  private getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.config.bearerToken}`,
      'X-API-Key': this.config.apiKey,
      'X-Auth-Key': this.config.authKey,
    };
  }

  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' = 'POST',
    body?: any,
  ): Promise<FlinksApiResponse<T>> {
    const url = `${this.config.apiUrl}/v3/${this.config.customerId}/BankingServices${endpoint}`;
    
    this.logger.log(`Making ${method} request to: ${url}`);

    try {
      const response = await fetch(url, {
        method,
        headers: this.getHeaders(),
        body: body ? JSON.stringify(body) : undefined,
      });

      const responseData = await response.json();

      if (!response.ok) {
        this.logger.error(`Flinks API error: ${response.status} - ${JSON.stringify(responseData)}`);
        throw new HttpException(
          {
            error: 'Flinks API Error',
            message: responseData.message || 'Unknown error occurred',
            statusCode: response.status,
            requestId: responseData.requestId,
          } as FlinksErrorResponse,
          response.status,
        );
      }

      this.logger.log(`Flinks API response: ${response.status}`);
      return {
        responseType: responseData.ResponseType || 'Success',
        httpStatusCode: response.status,
        data: responseData,
        requestId: responseData.RequestId,
      };
    } catch (error) {
      this.logger.error(`Flinks API request failed: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        {
          error: 'Network Error',
          message: 'Failed to connect to Flinks API',
          statusCode: HttpStatus.SERVICE_UNAVAILABLE,
        } as FlinksErrorResponse,
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  async getAccountsDetail(
    loginId: string,
    requestId?: string,
    withTransactions = true,
    withAccountIdentity = true,
    withBalance = true,
  ): Promise<GetAccountsDetailResponse> {
    const body = {
      LoginId: loginId,
      RequestId: requestId || `req_${Date.now()}`,
      WithTransactions: withTransactions,
      WithAccountIdentity: withAccountIdentity,
      WithBalance: withBalance,
    };

    const response = await this.makeRequest<GetAccountsDetailResponse>('/GetAccountsDetail', 'POST', body);
    return response.data!;
  }

  async getAccountsSummary(
    loginId: string,
    requestId?: string,
  ): Promise<GetAccountsSummaryResponse> {
    const body = {
      LoginId: loginId,
      RequestId: requestId || `req_${Date.now()}`,
    };

    const response = await this.makeRequest<GetAccountsSummaryResponse>('/GetAccountsSummary', 'POST', body);
    return response.data!;
  }

  async getInstitutions(): Promise<GetInstitutionsResponse> {
    const response = await this.makeRequest<GetInstitutionsResponse>('/Institutions', 'GET');
    return response.data!;
  }

  async getInstitutionByRoutingNumber(routingNumber: string): Promise<FlinksInstitution> {
    const response = await this.makeRequest<FlinksInstitution>(
      `/Institutions/RoutingNumber/${routingNumber}`,
      'GET',
    );
    return response.data!;
  }

  async validateLoginId(loginId: string): Promise<boolean> {
    try {
      await this.getAccountsSummary(loginId);
      return true;
    } catch (error) {
      this.logger.warn(`Login ID validation failed for ${loginId}: ${error.message}`);
      return false;
    }
  }

  getConfig(): Readonly<FlinksConfig> {
    return { ...this.config };
  }
}
