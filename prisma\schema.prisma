generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model AccountHolder {
  id                  String               @id @default(cuid())
  customerId          String
  userId              String
  firstName           String
  lastName            String
  email               String?
  phoneNumber         String?
  addressLine1        String?
  addressLine2        String?
  city                String?
  province            String?
  postalCode          String?
  country             String?
  dateOfBirth         DateTime?
  ssnCiphertext       String?              @db.Text
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt

  customer            Customer             @relation(fields: [customerId], references: [id])
  user                User                 @relation(fields: [userId], references: [id])
  institutionAccounts InstitutionAccount[] @relation("AccountHolderAccounts")

  @@unique([customerId, userId])
}

model InstitutionAccount {
  id                String          @id @default(cuid())
  accountNumber     String          @db.VarChar(255)
  balance           Decimal         @db.Decimal(18, 2)
  currentBalance    Decimal         @db.Decimal(18, 2)
  availableBalance  Decimal         @db.Decimal(18, 2)
  currency          String          @db.VarChar(10)
  routingNumber     String
  accountType       String?
  institutionId     String
  userId            String
  accessToken       String
  isFlinks          Boolean         @default(true)
  latencyKey        String?         @db.VarChar(255)
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  institution       BankInstitution @relation(fields: [institutionId], references: [id])
  accountHolders    AccountHolder[] @relation("AccountHolderAccounts")
  user              User            @relation(fields: [userId], references: [id])

  @@unique([accountNumber, institutionId])
}

model BankInstitution {
  id                  String               @id @default(cuid())
  name                String
  logoUrl             String?
  flinksInstitutionId String               @unique
  transitNumber       String?
  address             String?
  city                String?
  province            String?
  postalCode          String?
  country             String?
  website             String?
  phoneNumber         String?
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt

  institutionAccounts InstitutionAccount[]
}

model User {
  id              String               @id @default(cuid())
  email           String              @unique
  hashedPassword  String?
  firstName       String?
  lastName        String?
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt

  accountHolders  AccountHolder[]
  accounts        InstitutionAccount[]
}

model Customer {
  id             String         @id @default(cuid())
  customerId     String        @unique
  email          String?       @unique
  phoneNumber    String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  accountHolders AccountHolder[]
}
