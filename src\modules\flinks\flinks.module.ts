import { Module } from '@nestjs/common';
import { ConfigModule } from '../../config/config.module';
import { PrismaModule } from '../../prisma/prisma.module';
import { FlinksController } from './controllers/flinks.controller';
import { FlinksApiService } from './services/flinks-api.service';
import { FlinksDataProcessorService } from './services/flinks-data-processor.service';
import { FlinksResolver } from './resolvers/flinks.resolver';

@Module({
  imports: [ConfigModule, PrismaModule],
  controllers: [FlinksController],
  providers: [FlinksApiService, FlinksDataProcessorService, FlinksResolver],
  exports: [FlinksApiService, FlinksDataProcessorService],
})
export class FlinksModule {}
