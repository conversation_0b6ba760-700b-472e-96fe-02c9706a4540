export interface Config {
    readonly API_PORT: number;
    readonly API_PREFIX: string;
    readonly SWAGGER_ENABLE: number;
    readonly JWT_SECRET: string;
    readonly JWT_ISSUER: string;
    readonly JWT_EXPIRATION?: string;
    readonly B2_KEY_ID: string;
    readonly B2_APP_KEY: string;
    readonly B2_BUCKET_ID: string;
    readonly B2_ENDPOINT: string;
    readonly PARTNER_WEBHOOK_URL?: string;
    readonly PARTNER_EMAIL_CONFIG?: {
        apiUrl: string;
        recipients?: string[];
        teamDistributionList?: string;
        escalationDistributionList?: string;
        defaultFrom?: string;
        replyTo?: string;
    };
    readonly SOFTWARE_PARTNER_WEBHOOK_URL?: string;
    readonly ALLOWED_ORIGINS?: string;
    readonly PORT: number;
    readonly AUTH_SERVICE_URL: string;
    readonly FLINKS_API_URL: string;
    readonly FLINKS_CUSTOMER_ID: string;
    readonly FLINKS_BEARER_TOKEN: string;
    readonly FLINKS_AUTH_KEY: string;
    readonly FLINKS_API_KEY: string;
}
