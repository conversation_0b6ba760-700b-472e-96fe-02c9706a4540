import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { Logger } from '@nestjs/common';
import { FlinksApiService } from '../services/flinks-api.service';
import { FlinksDataProcessorService } from '../services/flinks-data-processor.service';

@Resolver()
export class FlinksResolver {
  private readonly logger = new Logger(FlinksResolver.name);

  constructor(
    private readonly flinksApiService: FlinksApiService,
    private readonly flinksDataProcessor: FlinksDataProcessorService,
  ) {}

  @Query(() => String, { description: 'Get account details from Flinks API' })
  async getFlinksAccountDetails(
    @Args('loginId') loginId: string,
    @Args('requestId', { nullable: true }) requestId?: string,
    @Args('withTransactions', { defaultValue: true }) withTransactions?: boolean,
    @Args('withAccountIdentity', { defaultValue: true }) withAccountIdentity?: boolean,
    @Args('withBalance', { defaultValue: true }) withBalance?: boolean,
  ): Promise<string> {
    this.logger.log(`GraphQL: Getting account details for loginId: ${loginId}`);

    try {
      const response = await this.flinksApiService.getAccountsDetail(
        loginId,
        requestId,
        withTransactions,
        withAccountIdentity,
        withBalance,
      );

      return JSON.stringify(response);
    } catch (error) {
      this.logger.error(`GraphQL: Failed to get account details: ${error.message}`);
      throw error;
    }
  }

  @Query(() => String, { description: 'Get account summary from Flinks API' })
  async getFlinksAccountSummary(
    @Args('loginId') loginId: string,
    @Args('requestId', { nullable: true }) requestId?: string,
  ): Promise<string> {
    this.logger.log(`GraphQL: Getting account summary for loginId: ${loginId}`);

    try {
      const response = await this.flinksApiService.getAccountsSummary(loginId, requestId);
      return JSON.stringify(response);
    } catch (error) {
      this.logger.error(`GraphQL: Failed to get account summary: ${error.message}`);
      throw error;
    }
  }

  @Query(() => String, { description: 'Get all supported institutions from Flinks' })
  async getFlinksInstitutions(): Promise<string> {
    this.logger.log('GraphQL: Getting all institutions from Flinks');

    try {
      const response = await this.flinksApiService.getInstitutions();
      return JSON.stringify(response);
    } catch (error) {
      this.logger.error(`GraphQL: Failed to get institutions: ${error.message}`);
      throw error;
    }
  }

  @Query(() => String, { description: 'Get institution by routing number' })
  async getFlinksInstitutionByRouting(
    @Args('routingNumber') routingNumber: string,
  ): Promise<string> {
    this.logger.log(`GraphQL: Getting institution for routing number: ${routingNumber}`);

    try {
      const response = await this.flinksApiService.getInstitutionByRoutingNumber(routingNumber);
      return JSON.stringify(response);
    } catch (error) {
      this.logger.error(`GraphQL: Failed to get institution by routing number: ${error.message}`);
      throw error;
    }
  }

  @Query(() => String, { description: 'Get account holders for a user' })
  async getUserAccountHolders(@Args('userId') userId: string): Promise<string> {
    this.logger.log(`GraphQL: Getting account holders for user: ${userId}`);

    try {
      const accountHolders = await this.flinksDataProcessor.getAccountHoldersByUser(userId);
      return JSON.stringify({
        success: true,
        data: accountHolders,
        count: accountHolders.length,
      });
    } catch (error) {
      this.logger.error(`GraphQL: Failed to get account holders: ${error.message}`);
      throw error;
    }
  }

  @Query(() => String, { description: 'Get institution accounts for a user' })
  async getUserInstitutionAccounts(@Args('userId') userId: string): Promise<string> {
    this.logger.log(`GraphQL: Getting institution accounts for user: ${userId}`);

    try {
      const accounts = await this.flinksDataProcessor.getInstitutionAccountsByUser(userId);
      return JSON.stringify({
        success: true,
        data: accounts,
        count: accounts.length,
      });
    } catch (error) {
      this.logger.error(`GraphQL: Failed to get institution accounts: ${error.message}`);
      throw error;
    }
  }

  @Query(() => String, { description: 'Get all bank institutions from database' })
  async getBankInstitutions(): Promise<string> {
    this.logger.log('GraphQL: Getting all bank institutions from database');

    try {
      const institutions = await this.flinksDataProcessor.getBankInstitutions();
      return JSON.stringify({
        success: true,
        data: institutions,
        count: institutions.length,
      });
    } catch (error) {
      this.logger.error(`GraphQL: Failed to get bank institutions: ${error.message}`);
      throw error;
    }
  }

  @Mutation(() => String, { description: 'Process and save Flinks account data to database' })
  async processFlinksData(
    @Args('userId') userId: string,
    @Args('customerId') customerId: string,
    @Args('loginId') loginId: string,
    @Args('requestId', { nullable: true }) requestId?: string,
  ): Promise<string> {
    this.logger.log(`GraphQL: Processing Flinks data for user ${userId}, customer ${customerId}`);

    try {
      // First, get the detailed account information from Flinks
      const flinksResponse = await this.flinksApiService.getAccountsDetail(
        loginId,
        requestId,
        true, // withTransactions
        true, // withAccountIdentity
        true, // withBalance
      );

      // Process and save the data to our database
      const processedData = await this.flinksDataProcessor.processAndSaveAccountData(
        userId,
        customerId,
        flinksResponse,
      );

      return JSON.stringify({
        success: true,
        message: 'Flinks data processed and saved successfully',
        data: {
          accountHoldersCount: processedData.accountHolders.length,
          institutionAccountsCount: processedData.institutionAccounts.length,
          bankInstitution: processedData.bankInstitution.name,
          requestId: flinksResponse.requestId,
        },
      });
    } catch (error) {
      this.logger.error(`GraphQL: Failed to process Flinks data: ${error.message}`);
      throw error;
    }
  }

  @Query(() => String, { description: 'Validate a Flinks login ID' })
  async validateFlinksLoginId(@Args('loginId') loginId: string): Promise<string> {
    this.logger.log(`GraphQL: Validating login ID: ${loginId}`);

    try {
      const isValid = await this.flinksApiService.validateLoginId(loginId);
      return JSON.stringify({
        loginId,
        isValid,
        message: isValid ? 'Login ID is valid' : 'Login ID is invalid or expired',
      });
    } catch (error) {
      this.logger.error(`GraphQL: Failed to validate login ID: ${error.message}`);
      return JSON.stringify({
        loginId,
        isValid: false,
        message: 'Failed to validate login ID',
        error: error.message,
      });
    }
  }
}
