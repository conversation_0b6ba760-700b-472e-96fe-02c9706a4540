# Flinks Integration Module

This module provides comprehensive integration with the Flinks API for open banking functionality. It handles account aggregation, institution data, and customer identity verification.

## Overview

The Flinks integration allows your openbanking frontend to:
1. Connect users to their bank accounts via Flinks Connect
2. Retrieve detailed account information and transaction history
3. Save account holder details and institution account data
4. Manage bank institution information

## Architecture

### Core Components

- **FlinksApiService**: HTTP client for Flinks API endpoints
- **FlinksDataProcessorService**: Processes and saves Flinks data to database
- **FlinksController**: REST API endpoints for Flinks operations
- **FlinksResolver**: GraphQL queries and mutations
- **DTOs**: Request/response data transfer objects
- **Interfaces**: TypeScript interfaces for type safety
- **Exceptions**: Custom error handling
- **Validators**: Input validation utilities

### Database Schema

The integration uses three main tables:
- **AccountHolder**: Customer/user account holder information
- **InstitutionAccount**: Bank account details from institutions
- **BankInstitution**: Financial institution information

## API Endpoints

### REST Endpoints

#### POST `/flinks/accounts/detail`
Get detailed account information from Flinks API.

**Request Body:**
```json
{
  "loginId": "uuid-from-flinks-connect",
  "requestId": "optional-tracking-id",
  "withTransactions": true,
  "withAccountIdentity": true,
  "withBalance": true
}
```

#### POST `/flinks/accounts/summary`
Get account summary from Flinks API.

**Request Body:**
```json
{
  "loginId": "uuid-from-flinks-connect",
  "requestId": "optional-tracking-id"
}
```

#### GET `/flinks/institutions`
Get all supported institutions from Flinks.

#### GET `/flinks/institutions/routing/:routingNumber`
Get institution by routing number.

#### POST `/flinks/process-data`
Process and save Flinks account data to database.

**Request Body:**
```json
{
  "userId": "your-user-id",
  "customerId": "your-customer-id",
  "loginId": "uuid-from-flinks-connect",
  "requestId": "optional-tracking-id"
}
```

#### GET `/flinks/user/:userId/account-holders`
Get account holders for a specific user.

#### GET `/flinks/user/:userId/institution-accounts`
Get institution accounts for a specific user.

#### GET `/flinks/bank-institutions`
Get all bank institutions from database.

#### GET `/flinks/validate-login/:loginId`
Validate a Flinks login ID.

### GraphQL Endpoints

Available at `/graphql`:

#### Queries
- `getFlinksAccountDetails(loginId, requestId?, withTransactions?, withAccountIdentity?, withBalance?)`
- `getFlinksAccountSummary(loginId, requestId?)`
- `getFlinksInstitutions`
- `getFlinksInstitutionByRouting(routingNumber)`
- `getUserAccountHolders(userId)`
- `getUserInstitutionAccounts(userId)`
- `getBankInstitutions`
- `validateFlinksLoginId(loginId)`

#### Mutations
- `processFlinksData(userId, customerId, loginId, requestId?)`

## Configuration

Set the following environment variables:

```env
FLINKS_API_URL=https://toolbox-api.private.fin.ag
FLINKS_CUSTOMER_ID=your-customer-id
FLINKS_BEARER_TOKEN=your-bearer-token
FLINKS_AUTH_KEY=your-auth-key
FLINKS_API_KEY=your-api-key
```

## Usage Flow

### 1. Frontend Flinks Connect Integration

Your frontend should integrate Flinks Connect to handle user authentication with their bank:

```javascript
// After user completes Flinks Connect flow
const loginId = "uuid-returned-from-flinks-connect";
const userId = "your-user-id";
const customerId = "your-customer-id";

// Process and save the account data
const response = await fetch('/flinks/process-data', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId,
    customerId,
    loginId
  })
});
```

### 2. Backend Processing

The backend will:
1. Call Flinks API to get detailed account information
2. Extract account holder details
3. Save/update AccountHolder record
4. Save/update InstitutionAccount records
5. Save/update BankInstitution record
6. Link account holders to institution accounts

### 3. Data Retrieval

Retrieve saved data using REST or GraphQL:

```javascript
// Get user's account holders
const accountHolders = await fetch(`/flinks/user/${userId}/account-holders`);

// Get user's institution accounts
const accounts = await fetch(`/flinks/user/${userId}/institution-accounts`);
```

## Error Handling

The module includes comprehensive error handling:

- **FlinksApiException**: Flinks API errors
- **FlinksConfigurationException**: Configuration errors
- **FlinksValidationException**: Input validation errors
- **FlinksDataProcessingException**: Data processing errors
- **FlinksNetworkException**: Network connectivity errors
- **FlinksAuthenticationException**: Authentication errors

## Validation

Input validation includes:
- Login ID format validation (UUID)
- User/Customer ID validation
- Routing number validation
- Email format validation
- Phone number format validation
- Postal code validation (CA/US)
- Date of birth validation

## Security Considerations

1. **API Keys**: Store Flinks credentials securely in environment variables
2. **Data Encryption**: Sensitive data like SSN is stored encrypted
3. **Access Control**: Implement proper authentication/authorization
4. **Logging**: Comprehensive logging without exposing sensitive data
5. **Validation**: All inputs are validated before processing

## Testing

The module is designed to be easily testable:
- Services are dependency-injected
- Comprehensive error handling
- Detailed logging for debugging
- Validation utilities for input checking

## Flinks API Documentation

For more information about Flinks API:
- [Flinks Documentation](https://docs.flinks.com/)
- [Customer Identity Verification](https://docs.flinks.com/docs/identify-your-customer)
- [API Reference](https://docs.flinks.com/reference)

## Support

This integration supports the primary Flinks API endpoints:
- `/GetAccountsDetail` - Detailed account information
- `/GetAccountsSummary` - Account summary
- `/Institutions` - Supported institutions
- `/Institutions/RoutingNumber/{routingNumber}` - Institution by routing number
