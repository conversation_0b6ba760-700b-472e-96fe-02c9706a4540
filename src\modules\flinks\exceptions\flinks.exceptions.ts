import { HttpException, HttpStatus } from '@nestjs/common';

export class FlinksApiException extends HttpException {
  constructor(
    message: string,
    statusCode: HttpStatus = HttpStatus.SERVICE_UNAVAILABLE,
    public readonly requestId?: string,
  ) {
    super(
      {
        error: 'Flinks API Error',
        message,
        statusCode,
        requestId,
        timestamp: new Date().toISOString(),
      },
      statusCode,
    );
  }
}

export class FlinksConfigurationException extends HttpException {
  constructor(message: string) {
    super(
      {
        error: 'Flinks Configuration Error',
        message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        timestamp: new Date().toISOString(),
      },
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}

export class FlinksValidationException extends HttpException {
  constructor(message: string, field?: string) {
    super(
      {
        error: 'Flinks Validation Error',
        message,
        field,
        statusCode: HttpStatus.BAD_REQUEST,
        timestamp: new Date().toISOString(),
      },
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class FlinksDataProcessingException extends HttpException {
  constructor(message: string, originalError?: any) {
    super(
      {
        error: 'Flinks Data Processing Error',
        message,
        originalError: originalError?.message || originalError,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        timestamp: new Date().toISOString(),
      },
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}

export class FlinksNetworkException extends HttpException {
  constructor(message: string = 'Failed to connect to Flinks API') {
    super(
      {
        error: 'Flinks Network Error',
        message,
        statusCode: HttpStatus.SERVICE_UNAVAILABLE,
        timestamp: new Date().toISOString(),
      },
      HttpStatus.SERVICE_UNAVAILABLE,
    );
  }
}

export class FlinksAuthenticationException extends HttpException {
  constructor(message: string = 'Flinks authentication failed') {
    super(
      {
        error: 'Flinks Authentication Error',
        message,
        statusCode: HttpStatus.UNAUTHORIZED,
        timestamp: new Date().toISOString(),
      },
      HttpStatus.UNAUTHORIZED,
    );
  }
}
