// src/main.ts
import { ValidationPipe } from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import { FastifyAdapter, NestFastifyApplication } from "@nestjs/platform-fastify";
import { SwaggerModule, DocumentBuilder } from "@nestjs/swagger";
import { AppModule } from "./app.module";
import { ConfigService } from "./config/config.service";

async function bootstrap() {
  // Determine environment
  const isDevOrTest = ["development", "test"].includes(process.env.NODE_ENV || "development");
  
  // Configure bind IP
  const sourceIp = isDevOrTest ? (process.env.SOURCE_IP || "127.0.0.1") : "*************";

  // Set up CORS configuration
  const allowedOrigins = (process.env.CORS_ORIGINS || "https://auth-fe.ngnair.com")
    .split(",")
    .map(origin => origin.trim());
  const corsSubdomain = process.env.CORS_SUBDOMAIN || "dev1";
  const subdomainRegex = new RegExp(`^https?://([a-z0-9-]+\\.)*${corsSubdomain}\\.ngnair\\.com$`, "i");
  const localhostRegex = /^http:\/\/(localhost|127\.0\.0\.1):\d+$/i;

  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter({ logger: true }),
    {
      cors: {
        origin: (origin, cb) => {
          if (
            !origin ||
            allowedOrigins.includes(origin) ||
            subdomainRegex.test(origin) ||
            (isDevOrTest && localhostRegex.test(origin))
          ) {
            cb(null, true);
          } else {
            cb(new Error("Not allowed by CORS"), false);
          }
        },
        credentials: true,
        methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "HEAD"],
        allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With", "Accept"],
      }
    }
  );

  // Initialize configuration service
  const configService = app.get(ConfigService);

  // Enable validation pipes
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
  }));

  // Configure Swagger
  const config = new DocumentBuilder()
    .setTitle("API Documentation")
    .setDescription("API Documentation")
    .setVersion("1.0")
    .addBearerAuth()
    .build();
    
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup("api", app, document);

  // Start the server
  const port = process.env.LISTENING_PORT || configService.get("PORT", 9000);
  await app.listen(port, sourceIp);

  // Log information about the running application
  const protocol = isDevOrTest ? "http" : "https";
  const hostname = isDevOrTest ? sourceIp : "api.ngnair.com"; // Adjust domain as needed
  const url = `${protocol}://${hostname}:${port}`;
  const apiDocsUrl = `${url}/api`;
  const graphqlUrl = `${url}/graphql`;

  /* eslint-disable no-console */
  console.log(`🚀 API is running on: ${url}`);
  console.log(`📚 Swagger documentation: ${apiDocsUrl}`);
  console.log(`🔗 GraphQL playground: ${graphqlUrl}`);
  /* eslint-enable no-console */
}

// Using void to mark that we're explicitly ignoring the promise
void bootstrap();
