# OpenBank API

A comprehensive banking API built with NestJS, Fastify, GraphQL, and Prisma. This API provides complete banking functionality including customer management, account operations, transactions, payments, cards, and loans.

## 🚀 Features

- **Fastify Integration**: High-performance web framework instead of Express
- **REST API**: Complete RESTful endpoints for all banking operations
- **GraphQL API**: Flexible GraphQL queries and mutations
- **Swagger Documentation**: Interactive API documentation
- **Authentication & Authorization**: JWT-based auth with role-based permissions
- **Database**: PostgreSQL with Prisma ORM
- **Testing**: Comprehensive unit tests with Jest
- **TypeScript**: Full type safety throughout the application

## 🏗️ Architecture

### Core Modules

1. **Customers Module**: Customer management and KYC operations
2. **Accounts Module**: Bank account operations (savings, checking, business, etc.)
3. **Transactions Module**: Transaction history and management
4. **Payments Module**: Payment processing and transfers
5. **Cards Module**: Debit/Credit card management
6. **Loans Module**: Loan products and applications

### Database Schema

The application uses a comprehensive banking schema with the following entities:

- **Customer**: Customer information and KYC data
- **Account**: Bank accounts with different types and statuses
- **Transaction**: All financial transactions
- **Payment**: Payment processing records
- **Card**: Debit and credit card information
- **Loan**: Loan applications and management
- **LoanPayment**: Loan payment history
- **AuditLog**: Complete audit trail for all operations

## 🛠️ Technology Stack

- **Framework**: NestJS with Fastify
- **Database**: PostgreSQL
- **ORM**: Prisma
- **GraphQL**: Apollo Server with Code-First approach
- **Documentation**: Swagger/OpenAPI
- **Testing**: Jest
- **Validation**: class-validator
- **Authentication**: JWT with custom guards

## 📚 API Documentation

### REST API

The REST API is available at the root level and includes:

- **Customers**: `/customers`
- **Accounts**: `/accounts`
- **Transactions**: `/transactions`
- **Payments**: `/payments`
- **Cards**: `/cards`
- **Loans**: `/loans`

### GraphQL API

GraphQL endpoint is available at `/graphql` with:

- **Queries**: Fetch customers, accounts, transactions, etc.
- **Mutations**: Create, update, and manage banking entities
- **Subscriptions**: Real-time updates (planned)

### Swagger Documentation

Interactive API documentation is available at `/api`

## 🔐 Authentication & Authorization

The API uses JWT-based authentication with role-based permissions:

### Banking Permissions

- **Customer Operations**: CREATE_CUSTOMER, READ_CUSTOMER, UPDATE_CUSTOMER, DELETE_CUSTOMER, VERIFY_KYC
- **Account Operations**: CREATE_ACCOUNT, READ_ACCOUNT, UPDATE_ACCOUNT, DELETE_ACCOUNT, FREEZE_ACCOUNT, CLOSE_ACCOUNT
- **Transaction Operations**: CREATE_TRANSACTION, READ_TRANSACTION, REVERSE_TRANSACTION
- **Payment Operations**: CREATE_PAYMENT, READ_PAYMENT, APPROVE_PAYMENT, CANCEL_PAYMENT
- **Card Operations**: CREATE_CARD, READ_CARD, UPDATE_CARD, BLOCK_CARD
- **Loan Operations**: CREATE_LOAN, READ_LOAN, UPDATE_LOAN, APPROVE_LOAN
- **Admin Operations**: ADMIN_ACCESS, AUDIT_ACCESS

## 🚦 Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL 12+
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   ```

4. Configure your database connection in `.env`:
   ```
   DATABASE_URL="postgresql://username:password@localhost:5432/openbank"
   ```

5. Run database migrations:
   ```bash
   npx prisma migrate dev
   ```

6. Start the development server:
   ```bash
   npm run dev
   ```

The API will be available at:
- **REST API**: http://localhost:3001
- **GraphQL**: http://localhost:3001/graphql
- **Swagger Docs**: http://localhost:3001/api

## 🧪 Testing

Run the test suite:

```bash
# Unit tests
npm test

# Test coverage
npm run test:cov

# E2E tests
npm run test:e2e
```

## 📝 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | 3001 |
| `DATABASE_URL` | PostgreSQL connection string | - |
| `API_PREFIX` | API route prefix | (empty - no prefix) |
| `ALLOWED_ORIGINS` | CORS allowed origins | http://localhost:3000 |
| `AUTH_SERVICE_URL` | Authentication service URL | http://localhost:3001 |

## 🔄 API Examples

### Create a Customer

```bash
curl -X POST http://localhost:3001/customers \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "dateOfBirth": "1990-01-01",
    "phone": "+**********"
  }'
```

### Create an Account

```bash
curl -X POST http://localhost:3001/accounts \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "accountType": "SAVINGS",
    "customerId": "CUST000001",
    "currency": "USD",
    "interestRate": 2.5
  }'
```

### GraphQL Query

```graphql
query GetCustomers {
  customers(page: 1, limit: 10) {
    id
    customerId
    firstName
    lastName
    email
    status
    kycVerified
    accounts {
      id
      accountNumber
      accountType
      balance
      currency
    }
  }
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions, please open an issue in the repository.
