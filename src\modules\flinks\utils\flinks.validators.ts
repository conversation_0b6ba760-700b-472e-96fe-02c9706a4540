import { FlinksValidationException } from '../exceptions/flinks.exceptions';

export class FlinksValidators {
  static validateLoginId(loginId: string): void {
    if (!loginId || typeof loginId !== 'string') {
      throw new FlinksValidationException('Login ID is required and must be a string', 'loginId');
    }

    if (loginId.trim().length === 0) {
      throw new FlinksValidationException('Login ID cannot be empty', 'loginId');
    }

    // Basic UUID format validation (Flinks typically uses UUIDs)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(loginId)) {
      throw new FlinksValidationException('Login ID must be a valid UUID format', 'loginId');
    }
  }

  static validateUserId(userId: string): void {
    if (!userId || typeof userId !== 'string') {
      throw new FlinksValidationException('User ID is required and must be a string', 'userId');
    }

    if (userId.trim().length === 0) {
      throw new FlinksValidationException('User ID cannot be empty', 'userId');
    }
  }

  static validateCustomerId(customerId: string): void {
    if (!customerId || typeof customerId !== 'string') {
      throw new FlinksValidationException('Customer ID is required and must be a string', 'customerId');
    }

    if (customerId.trim().length === 0) {
      throw new FlinksValidationException('Customer ID cannot be empty', 'customerId');
    }
  }

  static validateRoutingNumber(routingNumber: string): void {
    if (!routingNumber || typeof routingNumber !== 'string') {
      throw new FlinksValidationException('Routing number is required and must be a string', 'routingNumber');
    }

    if (routingNumber.trim().length === 0) {
      throw new FlinksValidationException('Routing number cannot be empty', 'routingNumber');
    }

    // Basic routing number validation (typically 9 digits in North America)
    const routingRegex = /^\d{9}$/;
    if (!routingRegex.test(routingNumber.replace(/\D/g, ''))) {
      throw new FlinksValidationException('Routing number must be 9 digits', 'routingNumber');
    }
  }

  static validateRequestId(requestId?: string): void {
    if (requestId && typeof requestId !== 'string') {
      throw new FlinksValidationException('Request ID must be a string', 'requestId');
    }

    if (requestId && requestId.trim().length === 0) {
      throw new FlinksValidationException('Request ID cannot be empty', 'requestId');
    }
  }

  static validateAccountNumber(accountNumber: string): void {
    if (!accountNumber || typeof accountNumber !== 'string') {
      throw new FlinksValidationException('Account number is required and must be a string', 'accountNumber');
    }

    if (accountNumber.trim().length === 0) {
      throw new FlinksValidationException('Account number cannot be empty', 'accountNumber');
    }

    // Basic account number validation (alphanumeric, 4-20 characters)
    const accountRegex = /^[a-zA-Z0-9]{4,20}$/;
    if (!accountRegex.test(accountNumber)) {
      throw new FlinksValidationException(
        'Account number must be alphanumeric and between 4-20 characters',
        'accountNumber',
      );
    }
  }

  static validateCurrency(currency: string): void {
    if (!currency || typeof currency !== 'string') {
      throw new FlinksValidationException('Currency is required and must be a string', 'currency');
    }

    // ISO 4217 currency code validation (3 uppercase letters)
    const currencyRegex = /^[A-Z]{3}$/;
    if (!currencyRegex.test(currency)) {
      throw new FlinksValidationException('Currency must be a valid 3-letter ISO code (e.g., CAD, USD)', 'currency');
    }
  }

  static validateAmount(amount: number): void {
    if (typeof amount !== 'number' || isNaN(amount)) {
      throw new FlinksValidationException('Amount must be a valid number', 'amount');
    }

    if (!isFinite(amount)) {
      throw new FlinksValidationException('Amount must be a finite number', 'amount');
    }
  }

  static validateEmail(email?: string): void {
    if (email && typeof email !== 'string') {
      throw new FlinksValidationException('Email must be a string', 'email');
    }

    if (email && email.trim().length > 0) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new FlinksValidationException('Email must be a valid email address', 'email');
      }
    }
  }

  static validatePhoneNumber(phoneNumber?: string): void {
    if (phoneNumber && typeof phoneNumber !== 'string') {
      throw new FlinksValidationException('Phone number must be a string', 'phoneNumber');
    }

    if (phoneNumber && phoneNumber.trim().length > 0) {
      // Basic phone number validation (digits, spaces, dashes, parentheses, plus sign)
      const phoneRegex = /^[\+]?[\d\s\-\(\)]{10,15}$/;
      if (!phoneRegex.test(phoneNumber)) {
        throw new FlinksValidationException(
          'Phone number must be 10-15 digits with optional formatting',
          'phoneNumber',
        );
      }
    }
  }

  static validatePostalCode(postalCode?: string, country?: string): void {
    if (postalCode && typeof postalCode !== 'string') {
      throw new FlinksValidationException('Postal code must be a string', 'postalCode');
    }

    if (postalCode && postalCode.trim().length > 0) {
      // Canadian postal code validation
      if (country === 'CA' || country === 'Canada') {
        const canadianPostalRegex = /^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/;
        if (!canadianPostalRegex.test(postalCode)) {
          throw new FlinksValidationException(
            'Canadian postal code must be in format A1A 1A1',
            'postalCode',
          );
        }
      }
      // US ZIP code validation
      else if (country === 'US' || country === 'USA' || country === 'United States') {
        const usZipRegex = /^\d{5}(-\d{4})?$/;
        if (!usZipRegex.test(postalCode)) {
          throw new FlinksValidationException(
            'US ZIP code must be in format 12345 or 12345-6789',
            'postalCode',
          );
        }
      }
    }
  }

  static validateDateOfBirth(dateOfBirth?: string): void {
    if (dateOfBirth && typeof dateOfBirth !== 'string') {
      throw new FlinksValidationException('Date of birth must be a string', 'dateOfBirth');
    }

    if (dateOfBirth && dateOfBirth.trim().length > 0) {
      const date = new Date(dateOfBirth);
      if (isNaN(date.getTime())) {
        throw new FlinksValidationException('Date of birth must be a valid date', 'dateOfBirth');
      }

      // Check if date is not in the future
      if (date > new Date()) {
        throw new FlinksValidationException('Date of birth cannot be in the future', 'dateOfBirth');
      }

      // Check if date is reasonable (not more than 150 years ago)
      const minDate = new Date();
      minDate.setFullYear(minDate.getFullYear() - 150);
      if (date < minDate) {
        throw new FlinksValidationException('Date of birth cannot be more than 150 years ago', 'dateOfBirth');
      }
    }
  }
}
