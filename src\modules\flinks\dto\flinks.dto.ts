import { IsString, IsOptional, IsBoolean, IsArray, IsN<PERSON>ber, IsDateString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class GetAccountsDetailRequestDto {
  @ApiProperty({ description: 'Login ID from Flinks Connect' })
  @IsString()
  loginId: string;

  @ApiPropertyOptional({ description: 'Request ID for tracking' })
  @IsOptional()
  @IsString()
  requestId?: string;

  @ApiPropertyOptional({ description: 'Include transactions in response', default: true })
  @IsOptional()
  @IsBoolean()
  withTransactions?: boolean = true;

  @ApiPropertyOptional({ description: 'Include account holder information', default: true })
  @IsOptional()
  @IsBoolean()
  withAccountIdentity?: boolean = true;

  @ApiPropertyOptional({ description: 'Include balance information', default: true })
  @IsOptional()
  @IsBoolean()
  withBalance?: boolean = true;
}

export class GetAccountsSummaryRequestDto {
  @ApiProperty({ description: 'Login ID from Flinks Connect' })
  @IsString()
  loginId: string;

  @ApiPropertyOptional({ description: 'Request ID for tracking' })
  @IsOptional()
  @IsString()
  requestId?: string;
}

export class GetInstitutionByRoutingNumberRequestDto {
  @ApiProperty({ description: 'Bank routing number' })
  @IsString()
  routingNumber: string;
}

export class FlinksAccountHolderDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  email?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  province?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  postalCode?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  country?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  dateOfBirth?: string;
}

export class FlinksBalanceDto {
  @ApiProperty()
  @IsNumber()
  available: number;

  @ApiProperty()
  @IsNumber()
  current: number;

  @ApiProperty()
  @IsNumber()
  limit?: number;
}

export class FlinksTransactionDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsNumber()
  amount: number;

  @ApiProperty()
  @IsDateString()
  date: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  code?: string;
}

export class FlinksAccountDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsString()
  accountNumber: string;

  @ApiProperty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsString()
  category: string;

  @ApiProperty()
  @IsString()
  currency: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  transitNumber?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  institutionNumber?: string;

  @ApiProperty({ type: FlinksBalanceDto })
  balance: FlinksBalanceDto;

  @ApiPropertyOptional({ type: FlinksAccountHolderDto })
  @IsOptional()
  holder?: FlinksAccountHolderDto;

  @ApiPropertyOptional({ type: [FlinksTransactionDto] })
  @IsOptional()
  @IsArray()
  transactions?: FlinksTransactionDto[];
}

export class FlinksLoginDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  username: string;

  @ApiProperty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsDateString()
  lastRefresh: string;

  @ApiProperty()
  @IsBoolean()
  isScheduledRefresh: boolean;
}

export class FlinksInstitutionDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  logoUrl?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  transitNumber?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  province?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  postalCode?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  country?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  website?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  phoneNumber?: string;
}

export class GetAccountsDetailResponseDto {
  @ApiProperty()
  @IsString()
  responseType: string;

  @ApiProperty()
  @IsNumber()
  httpStatusCode: number;

  @ApiProperty({ type: [FlinksAccountDto] })
  @IsArray()
  accounts: FlinksAccountDto[];

  @ApiProperty({ type: FlinksLoginDto })
  login: FlinksLoginDto;

  @ApiProperty()
  @IsString()
  institution: string;

  @ApiProperty()
  @IsString()
  requestId: string;
}

export class GetAccountsSummaryResponseDto {
  @ApiProperty()
  @IsString()
  responseType: string;

  @ApiProperty()
  @IsNumber()
  httpStatusCode: number;

  @ApiProperty({ type: [FlinksAccountDto] })
  @IsArray()
  accounts: FlinksAccountDto[];

  @ApiProperty({ type: FlinksLoginDto })
  login: FlinksLoginDto;

  @ApiProperty()
  @IsString()
  institution: string;

  @ApiProperty()
  @IsString()
  requestId: string;
}

export class GetInstitutionsResponseDto {
  @ApiProperty({ type: [FlinksInstitutionDto] })
  @IsArray()
  institutions: FlinksInstitutionDto[];
}

export class ProcessFlinksDataRequestDto {
  @ApiProperty({ description: 'User ID from your system' })
  @IsString()
  userId: string;

  @ApiProperty({ description: 'Customer ID from your system' })
  @IsString()
  customerId: string;

  @ApiProperty({ description: 'Login ID from Flinks Connect' })
  @IsString()
  loginId: string;

  @ApiPropertyOptional({ description: 'Request ID for tracking' })
  @IsOptional()
  @IsString()
  requestId?: string;
}
