# OpenBank API Environment Configuration

# Server Configuration
PORT=3001
NODE_ENV=development

# API Configuration
API_PREFIX=

# Database Configuration (OpenBank)
DATABASE_URL="postgresql://openbank:password@localhost:5432/openbank?schema=public"

# CORS Configuration
ALLOWED_ORIGINS="http://localhost:3000,http://localhost:3001"

# Authentication Configuration
AUTH_SERVICE_URL="http://localhost:3001"
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
API_KEY="your-api-key-for-testing"

# Access to the health route
HEALTH_TOKEN=ThisMustBeChanged

# Banking Configuration
BANK_NAME="OpenBank"
BANK_CODE="OPBK"
BANK_COUNTRY="US"

# Swagger API documentation
SWAGGER_ENABLE=true
ENABLE_SWAGGER=true
ENABLE_GRAPHQL_PLAYGROUND=true
ENABLE_CORS=true

# Logging configuration
LOG=true
LOG_LEVEL=debug

# OpenObserve Logging configuration
OTEL_LOGS=false
# OTEL_USER=<EMAIL>
# OTEL_PASSWORD=YourPassword
# OTEL_HOST=https://ngnair-logs.ngcap.ngnair.com
# OTEL_ORG=default
# OTEL_STREAM=quickstart1

# Redis PubSub configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET="your-session-secret-change-this-in-production"

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# File Upload Configuration
MAX_FILE_SIZE=********  # 10MB
UPLOAD_PATH="./uploads"

# Flinks API Configuration
# Get these credentials from your Flinks dashboard
FLINKS_API_URL=https://toolbox-api.private.fin.ag
FLINKS_CUSTOMER_ID=your-flinks-customer-id
FLINKS_BEARER_TOKEN=your-flinks-bearer-token
FLINKS_AUTH_KEY=your-flinks-auth-key
FLINKS_API_KEY=your-flinks-api-key

# External Services (Optional)
# PAYMENT_GATEWAY_URL=""
# PAYMENT_GATEWAY_API_KEY=""
# KYC_SERVICE_URL=""
# KYC_SERVICE_API_KEY=""