import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '../../../config/config.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { FlinksApiService } from '../services/flinks-api.service';
import { FlinksDataProcessorService } from '../services/flinks-data-processor.service';
import { FlinksController } from '../controllers/flinks.controller';

describe('Flinks Integration', () => {
  let module: TestingModule;
  let flinksApiService: FlinksApiService;
  let flinksDataProcessor: FlinksDataProcessorService;
  let flinksController: FlinksController;
  let configService: ConfigService;
  let prismaService: PrismaService;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      providers: [
        FlinksApiService,
        FlinksDataProcessorService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const config = {
                FLINKS_API_URL: 'https://test-api.flinks.com',
                FLINKS_CUSTOMER_ID: 'test-customer-id',
                FLINKS_BEARER_TOKEN: 'test-bearer-token',
                FLINKS_AUTH_KEY: 'test-auth-key',
                FLINKS_API_KEY: 'test-api-key',
              };
              return config[key];
            }),
          },
        },
        {
          provide: PrismaService,
          useValue: {
            accountHolder: {
              findFirst: jest.fn(),
              upsert: jest.fn(),
            },
            institutionAccount: {
              findFirst: jest.fn(),
              upsert: jest.fn(),
              update: jest.fn(),
            },
            bankInstitution: {
              findFirst: jest.fn(),
              create: jest.fn(),
              findMany: jest.fn(),
            },
          },
        },
      ],
      controllers: [FlinksController],
    }).compile();

    flinksApiService = module.get<FlinksApiService>(FlinksApiService);
    flinksDataProcessor = module.get<FlinksDataProcessorService>(FlinksDataProcessorService);
    flinksController = module.get<FlinksController>(FlinksController);
    configService = module.get<ConfigService>(ConfigService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterAll(async () => {
    await module.close();
  });

  describe('FlinksApiService', () => {
    it('should be defined', () => {
      expect(flinksApiService).toBeDefined();
    });

    it('should have valid configuration', () => {
      const config = flinksApiService.getConfig();
      expect(config.apiUrl).toBe('https://test-api.flinks.com');
      expect(config.customerId).toBe('test-customer-id');
      expect(config.bearerToken).toBe('test-bearer-token');
      expect(config.authKey).toBe('test-auth-key');
      expect(config.apiKey).toBe('test-api-key');
    });

    it('should validate login ID format', async () => {
      const validLoginId = '123e4567-e89b-12d3-a456-************';
      const result = await flinksApiService.validateLoginId(validLoginId);
      // This will fail in test environment, but validates the method exists
      expect(typeof result).toBe('boolean');
    });
  });

  describe('FlinksDataProcessorService', () => {
    it('should be defined', () => {
      expect(flinksDataProcessor).toBeDefined();
    });

    it('should have database access methods', () => {
      expect(flinksDataProcessor.getAccountHoldersByUser).toBeDefined();
      expect(flinksDataProcessor.getInstitutionAccountsByUser).toBeDefined();
      expect(flinksDataProcessor.getBankInstitutions).toBeDefined();
    });
  });

  describe('FlinksController', () => {
    it('should be defined', () => {
      expect(flinksController).toBeDefined();
    });

    it('should have all required endpoints', () => {
      expect(flinksController.getAccountsDetail).toBeDefined();
      expect(flinksController.getAccountsSummary).toBeDefined();
      expect(flinksController.getInstitutions).toBeDefined();
      expect(flinksController.getInstitutionByRoutingNumber).toBeDefined();
      expect(flinksController.processFlinksData).toBeDefined();
      expect(flinksController.validateLoginId).toBeDefined();
      expect(flinksController.getUserAccountHolders).toBeDefined();
      expect(flinksController.getUserInstitutionAccounts).toBeDefined();
      expect(flinksController.getBankInstitutions).toBeDefined();
    });
  });

  describe('Configuration', () => {
    it('should load all required Flinks configuration', () => {
      expect(configService.get('FLINKS_API_URL')).toBe('https://test-api.flinks.com');
      expect(configService.get('FLINKS_CUSTOMER_ID')).toBe('test-customer-id');
      expect(configService.get('FLINKS_BEARER_TOKEN')).toBe('test-bearer-token');
      expect(configService.get('FLINKS_AUTH_KEY')).toBe('test-auth-key');
      expect(configService.get('FLINKS_API_KEY')).toBe('test-api-key');
    });
  });

  describe('Database Integration', () => {
    it('should have Prisma service available', () => {
      expect(prismaService).toBeDefined();
      expect(prismaService.accountHolder).toBeDefined();
      expect(prismaService.institutionAccount).toBeDefined();
      expect(prismaService.bankInstitution).toBeDefined();
    });
  });
});
